<template>
  <view class="profile-container">
    <!-- 现代化头部区域 -->
    <view class="hero-header">
      <!-- 装饰性背景元素 -->
      <view class="hero-bg-decoration"></view>
      <view class="hero-bg-blur"></view>
      
      <!-- 头部内容 -->
      <view class="hero-content">
        <!-- 用户头像区域 -->
        <view class="user-avatar-section">
          <view class="avatar-container">
            <image
              class="user-avatar"
              :src="userInfo.avatar || '/static/default-avatar.svg'"
              mode="aspectFill"
            />
            <view v-if="userInfo.isLogin" class="status-badge online">
              <text class="status-dot"></text>
            </view>
            <view v-else class="status-badge offline">
              <text class="status-dot"></text>
            </view>
          </view>
        </view>

        <!-- 用户信息 -->
        <view class="user-profile-info">
          <text class="user-display-name">{{ userInfo.nickname || '智能字幕用户' }}</text>
          <text class="user-identity">{{ getUserIdText() }}</text>
          <view v-if="userInfo.isLogin" class="verified-badge">
            <text class="verified-icon">✓</text>
            <text class="verified-text">已认证</text>
          </view>
        </view>

        <!-- 登录按钮（未登录时显示） -->
        <view v-if="!userInfo.isLogin" class="login-action">
          <button @click="handleModernWechatLogin" class="modern-login-btn">
            <view class="login-btn-content">
              <text class="login-icon">🔐</text>
              <text class="login-text">微信快速登录</text>
            </view>
          </button>
          <text class="login-benefit">登录后获得更多专属功能</text>
        </view>
      </view>
    </view>

    <!-- 数据概览卡片 -->
    <view class="overview-section">
      <view v-if="userInfo.isLogin" class="data-overview">
        <!-- 加载状态 -->
        <view v-if="isLoadingStats" class="loading-skeleton">
          <view class="skeleton-card"></view>
          <view class="skeleton-card"></view>
          <view class="skeleton-card"></view>
        </view>

        <!-- 统计数据网格 -->
        <view v-else class="stats-grid">
          <view class="stat-card primary">
            <view class="stat-icon-bg">
              <text class="stat-emoji">📊</text>
            </view>
            <view class="stat-info">
              <text class="stat-value">{{ stats.totalTasks }}</text>
              <text class="stat-label">总任务数</text>
            </view>
          </view>

          <view class="stat-card success">
            <view class="stat-icon-bg">
              <text class="stat-emoji">✅</text>
            </view>
            <view class="stat-info">
              <text class="stat-value">{{ stats.completedTasks }}</text>
              <text class="stat-label">已完成</text>
            </view>
          </view>

          <view class="stat-card info">
            <view class="stat-icon-bg">
              <text class="stat-emoji">⏱️</text>
            </view>
            <view class="stat-info">
              <text class="stat-value">{{ formatDuration(stats.totalDuration) }}</text>
              <text class="stat-label">总时长</text>
            </view>
          </view>

          <view class="stat-card warning">
            <view class="stat-icon-bg">
              <text class="stat-emoji">📁</text>
            </view>
            <view class="stat-info">
              <text class="stat-value">{{ formatFileSize(stats.totalSize) }}</text>
              <text class="stat-label">处理文件</text>
            </view>
          </view>
        </view>

        <!-- 最近活动时间线 -->
        <view v-if="!isLoadingStats && recentActivities.length > 0" class="activity-timeline">
          <view class="timeline-header">
            <text class="timeline-title">最近活动</text>
            <text class="timeline-subtitle">{{ recentActivities.length }} 条记录</text>
          </view>
          <view class="timeline-list">
            <view
              v-for="(activity, index) in recentActivities.slice(0, 4)"
              :key="activity.id"
              class="timeline-item"
            >
              <view class="timeline-dot">
                <text class="timeline-emoji">{{ getActivityIcon(activity.type) }}</text>
              </view>
              <view class="timeline-content">
                <text class="timeline-text">{{ activity.description }}</text>
                <text class="timeline-time">{{ formatTime(activity.createdAt) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 未登录状态 -->
      <view v-else class="empty-state">
        <view class="empty-illustration">
          <text class="empty-icon">📊</text>
        </view>
        <text class="empty-title">登录查看数据</text>
        <text class="empty-description">登录后可查看您的使用统计和活动记录</text>
      </view>
    </view>

    <!-- 功能菜单（仅登录后显示） -->
    <view v-if="userInfo.isLogin" class="features-section">
      <view class="section-title-bar">
        <text class="section-main-title">功能中心</text>
        <text class="section-sub-title">管理您的账户和偏好设置</text>
      </view>

      <view class="feature-menu-grid">
        <view class="feature-menu-item" @click="showSettings">
          <view class="feature-icon-wrapper settings">
            <text class="feature-icon">⚙️</text>
          </view>
          <view class="feature-text-content">
            <text class="feature-name">系统设置</text>
            <text class="feature-desc">个性化配置选项</text>
          </view>
          <view class="feature-chevron">
            <text class="chevron-icon">›</text>
          </view>
        </view>

        <view class="feature-menu-item" @click="showAbout">
          <view class="feature-icon-wrapper about">
            <text class="feature-icon">ℹ️</text>
          </view>
          <view class="feature-text-content">
            <text class="feature-name">关于应用</text>
            <text class="feature-desc">版本信息与介绍</text>
          </view>
          <view class="feature-chevron">
            <text class="chevron-icon">›</text>
          </view>
        </view>

        <view class="feature-menu-item" @click="showHelp">
          <view class="feature-icon-wrapper help">
            <text class="feature-icon">💬</text>
          </view>
          <view class="feature-text-content">
            <text class="feature-name">帮助支持</text>
            <text class="feature-desc">常见问题与反馈</text>
          </view>
          <view class="feature-chevron">
            <text class="chevron-icon">›</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部应用信息 -->
    <view class="app-footer">
      <view class="app-info-card">
        <view class="app-brand">
          <text class="app-logo">🎬</text>
          <view class="brand-text">
            <text class="app-name">智能字幕胶囊</text>
            <text class="app-version">Version 1.0.0</text>
          </view>
        </view>
        <text class="app-copyright">© 2025   智能字幕胶囊团队</text>
        <text class="app-description">AI驱动的视频字幕生成工具</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getDeviceId, formatFileSize, formatDuration, formatTime } from '@/utils/common'
import { getWechatOpenid } from '@/utils/api'

// 用户信息
const userInfo = ref({
  nickname: '',
  avatar: '',
  deviceId: '',
  openid: '',
  isLogin: false
})

// 统计信息
const stats = ref({
  totalTasks: 0,
  completedTasks: 0,
  failedTasks: 0,
  cancelledTasks: 0,
  totalDuration: 0,
  totalSize: 0,
  averageProcessTime: 0
})

// 最近活动
const recentActivities = ref([])

// 加载状态
const isLoadingStats = ref(false)

// 页面加载时获取用户信息和统计数据
onMounted(async () => {
  await loadUserInfo()
  if (userInfo.value.isLogin) {
    await loadStats()
    await loadRecentActivities()
  }
})

// 加载用户信息
const loadUserInfo = async () => {
  try {
    const deviceId = getDeviceId()
    userInfo.value.deviceId = deviceId

    // 尝试从本地存储获取用户信息
    const savedUserInfo = uni.getStorageSync('userInfo')
    if (savedUserInfo) {
      userInfo.value.nickname = savedUserInfo.nickname
      userInfo.value.avatar = savedUserInfo.avatar
      userInfo.value.openid = savedUserInfo.openid || ''
      userInfo.value.isLogin = true
    } else {
      userInfo.value.nickname = `用户${deviceId.slice(-6)}`
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

// 获取用户ID显示文本
const getUserIdText = (): string => {
  if (userInfo.value.isLogin) {
    return `ID: ${userInfo.value.deviceId?.slice(-8) || 'N/A'}`
  }
  return '点击登录获取更多功能'
}



// 加载统计数据
const loadStats = async () => {
  if (!userInfo.value.isLogin || !userInfo.value.openid) {
    return
  }

  try {
    isLoadingStats.value = true

    // 调用云函数获取用户统计数据
    const result = await uniCloud.callFunction({
      name: 'get-user-stats',
      data: {
        openid: userInfo.value.openid
      }
    })

    console.log('用户统计数据:', result)

    if (result.result.code === 200) {
      const data = result.result.data
      stats.value = {
        totalTasks: data.totalTasks || 0,
        completedTasks: data.completedTasks || 0,
        failedTasks: data.failedTasks || 0,
        cancelledTasks: data.cancelledTasks || 0,
        totalDuration: data.totalDuration || 0,
        totalSize: data.totalSize || 0,
        averageProcessTime: data.averageProcessTime || 0
      }
    } else {
      console.error('获取统计数据失败:', result.result.message)
    }

  } catch (error) {
    console.error('加载统计数据失败:', error)
  } finally {
    isLoadingStats.value = false
  }
}

// 加载最近活动
const loadRecentActivities = async () => {
  if (!userInfo.value.isLogin || !userInfo.value.openid) {
    return
  }

  try {
    const result = await uniCloud.callFunction({
      name: 'get-user-activities',
      data: {
        openid: userInfo.value.openid,
        limit: 5
      }
    })

    if (result.result.code === 200) {
      recentActivities.value = result.result.data || []
    }

  } catch (error) {
    console.error('加载最近活动失败:', error)
  }
}



// 获取活动图标
const getActivityIcon = (type: string): string => {
  switch (type) {
    case 'task_created': return '📤'
    case 'task_completed': return '✅'
    case 'task_failed': return '❌'
    case 'task_cancelled': return '⏹️'
    case 'login': return '🔐'
    default: return '📋'
  }
}



// 现代化微信登录（备用方案）
const handleModernWechatLogin = async () => {
  try {
    // 第一步：直接调用 uni.login 获取 code
    uni.showLoading({ title: '登录中...' })

    const loginRes = await new Promise<any>((resolve, reject) => {
      uni.login({
        provider: 'weixin',
        success: resolve,
        fail: reject
      })
    })

    if (!loginRes.code) {
      throw new Error('获取登录凭证失败')
    }

    // 第二步：调用服务端接口获取 openid
    const openidResult = await getWechatOpenid(loginRes.code)

    if (openidResult.errCode !== 0) {
      throw new Error(openidResult.errMsg || '获取openid失败')
    }

    const { openid, isNewUser } = openidResult.data || {}

    if (!openid) {
      throw new Error('获取用户openid失败')
    }

    // 第三步：更新本地用户信息（使用默认头像和昵称）
    userInfo.value.nickname = `用户${openid.slice(-6)}`
    userInfo.value.avatar = '/static/default-avatar.svg'
    userInfo.value.openid = openid
    userInfo.value.isLogin = true

    // 保存用户信息到本地存储
    const userInfoToSave = {
      nickname: userInfo.value.nickname,
      avatar: userInfo.value.avatar,
      openid: userInfo.value.openid,
      loginTime: Date.now(),
      isNewUser: isNewUser
    }

    uni.setStorageSync('userInfo', userInfoToSave)
    uni.hideLoading()

    uni.showToast({
      title: isNewUser ? '注册成功' : '登录成功',
      icon: 'success'
    })

    // 登录成功后加载统计数据
    await loadStats()
    await loadRecentActivities()

  } catch (error: any) {
    uni.hideLoading()
    console.error('现代化微信登录失败:', error)

    uni.showToast({
      title:JSON.stringify(error),
      icon: 'none',
      duration:10000000
    })
  }
}



// 页面导航
const navigateTo = (url: string) => {
  uni.navigateTo({ url })
}

// 显示关于信息
const showAbout = () => {
  uni.showModal({
    title: '关于我们',
    content: '智能字幕胶囊是一款AI驱动的视频字幕生成工具，支持视频添加字幕、字幕翻译、视频去水印等功能。',
    showCancel: false
  })
}

// 显示设置
const showSettings = () => {
  uni.showActionSheet({
    itemList: ['清除缓存'],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          clearCache()
          break
        case 1:
          uni.showToast({
            title: '推送设置功能开发中',
            icon: 'none'
          })
          break
        case 2:
          uni.showToast({
            title: '隐私设置功能开发中',
            icon: 'none'
          })
          break
      }
    }
  })
}

// 清除缓存
const clearCache = () => {
  uni.showModal({
    title: '清除缓存',
    content: '确定要清除所有缓存数据吗？',
    success: (res) => {
      if (res.confirm) {
        try {
          uni.clearStorageSync()
          uni.showToast({
            title: '缓存清除成功',
            icon: 'success'
          })
        } catch (error) {
          uni.showToast({
            title: '清除失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 显示帮助信息
const showHelp = () => {
  uni.showModal({
    title: '帮助与反馈',
    content: '如有问题或建议，请联系 wx zhoumaoning123。我们将竭诚为您服务！',
    showCancel: false
  })
}
</script>

<style scoped>
/* ==================== 全局容器 ==================== */
.profile-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  position: relative;
  overflow-x: hidden;
}

/* ==================== 现代化头部区域 ==================== */
.hero-header {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  padding: 80rpx 32rpx 60rpx;
  position: relative;
  overflow: hidden;
  border-radius: 0 0 40rpx 40rpx;
  margin-bottom: 40rpx;
}

/* 装饰性背景元素 */
.hero-bg-decoration {
  position: absolute;
  top: -50%;
  right: -30%;
  width: 400rpx;
  height: 400rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.hero-bg-blur {
  position: absolute;
  bottom: -20%;
  left: -20%;
  width: 300rpx;
  height: 300rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 8s ease-in-out infinite reverse;
}

.hero-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 32rpx;
}

/* 用户头像区域 */
.user-avatar-section {
  position: relative;
}

.avatar-container {
  position: relative;
  display: inline-block;
}

.user-avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 70rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 
    0 20rpx 60rpx rgba(0, 0, 0, 0.25),
    0 0 0 1rpx rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-avatar:hover {
  transform: translateY(-4rpx) scale(1.05);
  box-shadow: 
    0 32rpx 80rpx rgba(0, 0, 0, 0.35),
    0 0 0 1rpx rgba(255, 255, 255, 0.2);
}

.status-badge {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  border: 4rpx solid white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.status-badge.online {
  background: linear-gradient(135deg, #22c55e, #16a34a);
}

.status-badge.offline {
  background: linear-gradient(135deg, #94a3b8, #64748b);
}

.status-dot {
  width: 8rpx;
  height: 8rpx;
  background: white;
  border-radius: 4rpx;
  animation: pulse-dot 2s ease-in-out infinite;
}

/* 用户信息 */
.user-profile-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.user-display-name {
  font-size: 42rpx;
  font-weight: 700;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5rpx;
}

.user-identity {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.85);
  font-weight: 500;
}

.verified-badge {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.verified-icon {
  font-size: 24rpx;
  color: #22c55e;
  font-weight: 700;
}

.verified-text {
  font-size: 24rpx;
  color: white;
  font-weight: 600;
}

/* 登录按钮 */
.login-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  margin-top: 16rpx;
}

.modern-login-btn {
  background: white;
  border: none;
  border-radius: 32rpx;
  padding: 0;
  box-shadow: 
    0 20rpx 50rpx rgba(0, 0, 0, 0.25),
    0 0 0 1rpx rgba(255, 255, 255, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.modern-login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  transition: left 0.6s ease;
}

.modern-login-btn:hover::before {
  left: 100%;
}

.modern-login-btn:hover {
  transform: translateY(-4rpx);
  box-shadow: 
    0 32rpx 64rpx rgba(0, 0, 0, 0.3),
    0 0 0 1rpx rgba(255, 255, 255, 0.9);
}

.modern-login-btn:active {
  transform: translateY(-2rpx);
  transition: all 0.15s ease;
}

.login-btn-content {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 40rpx;
  position: relative;
  z-index: 1;
}

.login-icon {
  font-size: 32rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(79, 70, 229, 0.3));
}

.login-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #4f46e5;
}

.login-benefit {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.3);
  font-weight: 500;
}

/* ==================== 数据概览区域 ==================== */
.overview-section {
  padding: 0 32rpx;
  margin-bottom: 40rpx;
}

/* 加载骨架屏 */
.loading-skeleton {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.skeleton-card {
  height: 120rpx;
  background: linear-gradient(90deg, #f1f5f9, #e2e8f0, #f1f5f9);
  background-size: 200% 100%;
  border-radius: 24rpx;
  animation: shimmer 1.5s ease-in-out infinite;
}

/* 统计数据网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 32rpx;
}

.stat-card {
  background: white;
  border-radius: 24rpx;
  padding: 28rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  transition: all 0.3s ease;
}

.stat-card.primary::before { background: linear-gradient(90deg, #6366f1, #4f46e5); }
.stat-card.success::before { background: linear-gradient(90deg, #22c55e, #16a34a); }
.stat-card.info::before { background: linear-gradient(90deg, #3b82f6, #2563eb); }
.stat-card.warning::before { background: linear-gradient(90deg, #f59e0b, #d97706); }

.stat-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.stat-icon-bg {
  width: 60rpx;
  height: 60rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
}

.stat-card.primary .stat-icon-bg { background: linear-gradient(135deg, #6366f1, #4f46e5); }
.stat-card.success .stat-icon-bg { background: linear-gradient(135deg, #22c55e, #16a34a); }
.stat-card.info .stat-icon-bg { background: linear-gradient(135deg, #3b82f6, #2563eb); }
.stat-card.warning .stat-icon-bg { background: linear-gradient(135deg, #f59e0b, #d97706); }

.stat-emoji {
  font-size: 28rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.stat-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
}

.stat-label {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
}

/* 活动时间线 */
.activity-timeline {
  background: white;
  border-radius: 24rpx;
  padding: 28rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f1f5f9;
}

.timeline-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
}

.timeline-subtitle {
  font-size: 24rpx;
  color: #6b7280;
}

.timeline-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.timeline-dot {
  width: 44rpx;
  height: 44rpx;
  border-radius: 22rpx;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.timeline-emoji {
  font-size: 20rpx;
}

.timeline-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.timeline-text {
  font-size: 28rpx;
  color: #374151;
  line-height: 1.4;
}

.timeline-time {
  font-size: 24rpx;
  color: #9ca3af;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 80rpx 32rpx;
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.empty-illustration {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
}

.empty-icon {
  font-size: 60rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8rpx;
}

.empty-description {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* ==================== 功能菜单区域 ==================== */
.features-section {
  padding: 0 32rpx;
  margin-bottom: 40rpx;
}

.section-title-bar {
  text-align: center;
  margin-bottom: 32rpx;
}

.section-main-title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.section-sub-title {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
}

.feature-menu-grid {
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.feature-menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f1f5f9;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.feature-menu-item:last-child {
  border-bottom: none;
}

.feature-menu-item:hover {
  background: #f8fafc;
}

.feature-menu-item:active {
  background: #f1f5f9;
  transform: scale(0.995);
}

.feature-icon-wrapper {
  width: 56rpx;
  height: 56rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.feature-icon-wrapper.settings { background: linear-gradient(135deg, #f1f5f9, #e2e8f0); }
.feature-icon-wrapper.about { background: linear-gradient(135deg, #eff6ff, #dbeafe); }
.feature-icon-wrapper.help { background: linear-gradient(135deg, #fffbeb, #fef3c7); }

.feature-menu-item:hover .feature-icon-wrapper {
  transform: scale(1.05);
}

.feature-icon {
  font-size: 32rpx;
}

.feature-text-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.feature-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
}

.feature-desc {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
}

.feature-chevron {
  width: 36rpx;
  height: 36rpx;
  border-radius: 18rpx;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.feature-menu-item:hover .feature-chevron {
  background: #e0e7ff;
  transform: translateX(4rpx);
}

.chevron-icon {
  font-size: 28rpx;
  color: #9ca3af;
  font-weight: 600;
}

.feature-menu-item:hover .chevron-icon {
  color: #6366f1;
}

/* ==================== 底部应用信息 ==================== */
.app-footer {
  padding: 0 32rpx 40rpx;
}

.app-info-card {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f1f5f9;
}

.app-brand {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.app-logo {
  font-size: 48rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.brand-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.app-name {
  font-size: 32rpx;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
}

.app-version {
  font-size: 22rpx;
  color: #9ca3af;
  font-weight: 500;
}

.app-copyright {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}

.app-description {
  display: block;
  font-size: 26rpx;
  color: #9ca3af;
  line-height: 1.5;
}

/* ==================== 动画效果 ==================== */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes pulse-dot {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 30rpx, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.overview-section,
.features-section,
.app-footer {
  animation: slideInUp 0.6s ease-out;
}

.features-section {
  animation-delay: 0.1s;
}

.app-footer {
  animation-delay: 0.2s;
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 375px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }
  
  .stat-card {
    padding: 24rpx;
  }
  
  .user-avatar {
    width: 120rpx;
    height: 120rpx;
  }
  
  .user-display-name {
    font-size: 36rpx;
  }
}

@media (min-width: 768px) {
  .profile-container {
    max-width: 750rpx;
    margin: 0 auto;
  }
  
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
</style>
