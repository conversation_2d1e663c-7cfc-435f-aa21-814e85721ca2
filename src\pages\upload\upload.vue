<template>
  <view class="upload-container">
    <!-- 现代化头部区域 -->
    <view class="hero-header">
      <view class="hero-background"></view>
      <view class="hero-content">
        <view class="hero-icon">
          <text class="icon-video">🎬</text>
          <view class="icon-glow"></view>
        </view>
        <view class="hero-text">
          <text class="hero-title">创作你的视频字幕</text>
          <text class="hero-subtitle">AI智能识别 · 多语言翻译 · 专业字幕</text>
        </view>
        <view class="hero-stats">
          <view class="stat-item">
            <text class="stat-number">98%</text>
            <text class="stat-label">识别精度</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-number">90+</text>
            <text class="stat-label">支持语言</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-number">5分钟</text>
            <text class="stat-label">平均处理</text>
          </view>
        </view>
      </view>
      <view class="hero-decoration">
        <view class="decoration-circle circle-1"></view>
        <view class="decoration-circle circle-2"></view>
        <view class="decoration-circle circle-3"></view>
      </view>
    </view>

    <!-- 核心功能区 -->
    <view class="content-wrapper">
      <!-- 未登录状态 -->
      <view v-if="!userInfo.isLogin" class="auth-section">
        <view class="auth-card">
          <view class="auth-visual">
            <view class="auth-icon">
              <text class="lock-icon">🔒</text>
              <view class="pulse-ring"></view>
            </view>
          </view>
          <view class="auth-content">
            <text class="auth-title">开启AI字幕之旅</text>
            <text class="auth-description">微信登录后即可享受专业级字幕制作服务</text>
            <view class="auth-features">
              <view class="feature-point">
                <text class="feature-icon">✨</text>
                <text class="feature-text">AI智能识别</text>
              </view>
              <view class="feature-point">
                <text class="feature-icon">🌍</text>
                <text class="feature-text">多语言支持</text>
              </view>
              <view class="feature-point">
                <text class="feature-icon">⚡</text>
                <text class="feature-text">极速处理</text>
              </view>
            </view>
          </view>
          <button @click="goToLogin" class="auth-button">
            <text class="btn-text">微信一键登录</text>
            <view class="btn-arrow">→</view>
          </button>
        </view>
      </view>

      <!-- 已登录状态 -->
      <template v-if="userInfo.isLogin">
        <!-- 模式选择卡片 -->
        <view class="mode-selector" v-if="!isUploading && !uploadSuccess">
          <view class="selector-header">
            <text class="selector-title">选择处理方式</text>
            <text class="selector-subtitle">根据视频来源选择最佳处理方案</text>
          </view>
          
          <view class="mode-cards">
            <view 
              class="mode-card upload-card" 
              :class="{ 'card-active': currentMode === 'upload' }"
              @click="switchMode('upload')"
            >
              <view class="card-visual">
                <view class="visual-icon upload-visual">
                  <text class="card-emoji">📱</text>
                </view>
                <view class="visual-decoration">
                  <view class="deco-dot dot-1"></view>
                  <view class="deco-dot dot-2"></view>
                  <view class="deco-dot dot-3"></view>
                </view>
              </view>
              <view class="card-content">
                <text class="card-title">本地上传</text>
                <text class="card-description">从相册选择或拍摄新视频</text>
                <view class="card-badges">
                  <text class="badge">高质量</text>
                  <text class="badge">无损处理</text>
                </view>
              </view>
              <view class="card-indicator">
                <view class="indicator-ring"></view>
                <view class="indicator-dot"></view>
              </view>
            </view>

            <view 
              class="mode-card link-card" 
              :class="{ 'card-active': currentMode === 'link' }"
              @click="switchMode('link')"
            >
              <view class="card-visual">
                <view class="visual-icon link-visual">
                  <text class="card-emoji">🔗</text>
                </view>
                <view class="visual-decoration">
                  <view class="deco-line line-1"></view>
                  <view class="deco-line line-2"></view>
                </view>
              </view>
              <view class="card-content">
                <text class="card-title">链接解析</text>
                <text class="card-description">后端智能下载，支持抖音、小红书</text>
                <view class="card-badges">
                  <text class="badge">智能处理</text>
                  <text class="badge">稳定可靠</text>
                </view>
              </view>
              <view class="card-indicator">
                <view class="indicator-ring"></view>
                <view class="indicator-dot"></view>
              </view>
            </view>
          </view>
        </view>

        <!-- 语言设置卡片 - 移动到显眼位置 -->
        <view v-if="!isUploading && !uploadSuccess" class="language-config-card">
          <view class="config-header">
            <view class="config-icon">
              <text class="icon-text">🌍</text>
            </view>
            <view class="config-content">
              <text class="config-title">语言配置</text>
              <text class="config-subtitle">设置识别和翻译语言，让AI更好理解您的需求</text>
            </view>
          </view>
          <view class="config-body">
            <LanguageSelector
              v-model:sourceLanguage="selectedSourceLanguage"
              v-model:targetLanguage="selectedTargetLanguage"
            />
          </view>
        </view>

        <!-- 上传区域 -->
        <view v-if="currentMode === 'upload' && !isUploading && !uploadSuccess" class="upload-section">
          <view class="upload-zone" @click="selectVideo">
            <view class="upload-visual">
              <view class="upload-circle">
                <text class="upload-icon">📁</text>
                <view class="upload-rings">
                  <view class="ring ring-1"></view>
                  <view class="ring ring-2"></view>
                  <view class="ring ring-3"></view>
                </view>
              </view>
            </view>
            <view class="upload-content">
              <text class="upload-title">点击选择视频文件</text>
              <text class="upload-subtitle">{{ uploadDescText }}</text>
              <view class="upload-specs">
                <view class="spec-item">
                  <text class="spec-icon">📏</text>
                  <text class="spec-text">最大200MB</text>
                </view>
                <view class="spec-divider">·</view>
                <view class="spec-item">
                  <text class="spec-icon">⏱️</text>
                  <text class="spec-text">10分钟以内</text>
                </view>
                <view class="spec-divider">·</view>
                <view class="spec-item">
                  <text class="spec-icon">🎞️</text>
                  <text class="spec-text">MP4/MOV</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 上传中的禁用状态提示 -->
        <view v-if="currentMode === 'upload' && isUploading" class="upload-disabled-section">
          <view class="upload-disabled-zone">
            <view class="disabled-visual">
              <view class="disabled-circle">
                <text class="disabled-icon">🔒</text>
              </view>
            </view>
            <view class="disabled-content">
              <text class="disabled-title">正在上传视频文件</text>
              <text class="disabled-subtitle">请等待上传完成后再选择新文件</text>
            </view>
          </view>
        </view>

        <!-- 链接解析区域 -->
        <view v-if="currentMode === 'link' && !isUploading && !uploadSuccess" class="link-section">
          <VideoLinkParser 
            :disabled="isUploading"
            :userInfo="userInfo"
            @parsed="handleParsedVideo"
            @error="handleParseError"
          />
        </view>

        <!-- 处理进度区域 -->
        <view v-if="isUploading" class="progress-section">
          <view class="progress-card">
            <view class="progress-visual">
              <view class="progress-circle">
                <view class="circle-progress" :style="{ '--progress': uploadProgress + '%' }">
                  <text class="progress-percent">{{ uploadProgress }}%</text>
                </view>
                <view class="pulse-waves">
                  <view class="wave wave-1"></view>
                  <view class="wave wave-2"></view>
                  <view class="wave wave-3"></view>
                </view>
              </view>
            </view>
            
            <view class="progress-info">
              <text class="progress-title">{{ uploadStatus || '正在上传视频文件...' }}</text>
              <text class="progress-subtitle">{{ getUploadDesc() }}</text>

              <view v-if="uploadProgress > 0 && uploadDetails.totalSize > 0" class="progress-details">
                <view class="detail-row">
                  <text class="detail-label">已上传</text>
                  <text class="detail-value">{{ formatFileSize(uploadDetails.uploadedSize) }} / {{ formatFileSize(uploadDetails.totalSize) }}</text>
                </view>
                <view class="detail-row" v-if="uploadDetails.uploadSpeed > 0">
                  <text class="detail-label">上传速度</text>
                  <text class="detail-value">{{ formatSpeed(uploadDetails.uploadSpeed) }}</text>
                </view>
                <view class="detail-row" v-if="uploadDetails.remainingTime > 0">
                  <text class="detail-label">预计剩余</text>
                  <text class="detail-value">{{ formatRemainingTime(uploadDetails.remainingTime) }}</text>
                </view>
              </view>

              <view v-else class="preparing-stage">
                <view class="loading-dots">
                  <view class="dot dot-1"></view>
                  <view class="dot dot-2"></view>
                  <view class="dot dot-3"></view>
                </view>
                <text class="preparing-text">正在准备上传，请稍候...</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 成功状态 -->
        <view v-if="uploadSuccess" class="success-section">
          <view class="success-card">
            <view class="success-visual">
              <view class="success-circle">
                <text class="success-emoji">🎉</text>
                <view class="success-sparkles">
                  <view class="sparkle sparkle-1">✨</view>
                  <view class="sparkle sparkle-2">⭐</view>
                  <view class="sparkle sparkle-3">💫</view>
                  <view class="sparkle sparkle-4">✨</view>
                </view>
              </view>
            </view>
            <view class="success-content">
              <text class="success-title">处理成功！</text>
              <text class="success-description">正在跳转到处理页面，请稍候...</text>
            </view>
          </view>
        </view>

        <!-- 处理能力展示面板 -->
        <view v-if="!isUploading && !uploadSuccess" class="capabilities-panel">
          <view class="panel-section features-panel">
            <view class="panel-header">
              <view class="header-icon">
                <text class="icon-text">⚡</text>
              </view>
              <view class="header-content">
                <text class="panel-title">AI处理能力</text>
                <text class="panel-subtitle">专业引擎支持，让您的视频更精彩</text>
              </view>
            </view>
            <view class="panel-body">
              <view class="capability-grid">
                <view class="capability-item">
                  <view class="capability-icon">🎯</view>
                  <view class="capability-info">
                    <text class="capability-title">精准识别</text>
                    <text class="capability-desc">98%+ 识别准确率</text>
                  </view>
                </view>
                
                <view class="capability-item">
                  <view class="capability-icon">🚀</view>
                  <view class="capability-info">
                    <text class="capability-title">极速处理</text>
                    <text class="capability-desc">平均5分钟完成</text>
                  </view>
                </view>
                
                <view class="capability-item">
                  <view class="capability-icon">🎨</view>
                  <view class="capability-info">
                    <text class="capability-title">专业字幕</text>
                    <text class="capability-desc">自动时间轴对齐</text>
                  </view>
                </view>
                
                <view class="capability-item">
                  <view class="capability-icon">🌐</view>
                  <view class="capability-info">
                    <text class="capability-title">多语言</text>
                    <text class="capability-desc">支持90+种语言</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </template>
    </view>

    <!-- 错误提示组件 -->
    <ErrorToast
      :visible="errorState.visible"
      :type="errorState.type"
      :title="errorState.title"
      :message="errorState.message"
      :details="errorState.details"
      :suggestions="errorState.suggestions"
      :showRetry="errorState.showRetry"
      @close="closeError"
      @retry="handleErrorRetry"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { onShow } from "@dcloudio/uni-app";
import type { SelectedVideo } from '@/types/video'
import { checkLoginStatus, handleLoginCheck, formatFileSize, formatSpeed, formatRemainingTime, type LoginCheckResult } from '@/utils/common'
import { getVideoUploadConfig, generateUploadDesc, formatMaxFileSize, formatMaxDuration } from '@/config'
import LanguageSelector from '@/components/LanguageSelector.vue'
import VideoLinkParser from '@/components/VideoLinkParser.vue'
import ErrorToast from '@/components/ErrorToast.vue'
// 移除VOD SDK引用，改用OSS直传

// 获取配置
const uploadConfig = getVideoUploadConfig()

// 计算属性：动态生成配置相关的文本
const uploadDescText = computed(() => generateUploadDesc(uploadConfig))

// 新增：模式选择
const currentMode = ref<'upload' | 'link'>('upload')

// 响应式数据
const isUploading = ref(false)
const selectedVideo = ref<SelectedVideo | null>(null)
const uploadProgress = ref(0)
const uploadStatus = ref('')
const currentTaskId = ref('')

// 新增：详细上传进度信息
const uploadDetails = ref({
  uploadedSize: 0,
  totalSize: 0,
  uploadSpeed: 0,
  remainingTime: 0,
  startTime: 0
})

// 新增：上传成功状态
const uploadSuccess = ref(false)

// 语言选择
const selectedSourceLanguage = ref('auto')
const selectedTargetLanguage = ref('zh')

// 用户信息
const userInfo = ref({
  openid: '',
  isLogin: false
})

// 错误状态管理
const errorState = ref({
  visible: false,
  type: 'error' as 'error' | 'warning' | 'info',
  title: '',
  message: '',
  details: '',
  suggestions: [] as string[],
  showRetry: false,
  retryAction: null as (() => void) | null
})

// 显示错误提示
const showError = (config: {
  type?: 'error' | 'warning' | 'info'
  title?: string
  message: string
  details?: string
  suggestions?: string[]
  showRetry?: boolean
  retryAction?: () => void
}) => {
  errorState.value = {
    visible: true,
    type: config.type || 'error',
    title: config.title || '',
    message: config.message,
    details: config.details || '',
    suggestions: config.suggestions || [],
    showRetry: config.showRetry || false,
    retryAction: config.retryAction || null
  }
}

// 关闭错误提示
const closeError = () => {
  errorState.value.visible = false
}

// 处理错误重试
const handleErrorRetry = () => {
  if (errorState.value.retryAction) {
    errorState.value.retryAction()
  }
}



/**
 * 切换上传模式（文件上传 / 链接解析）
 * 页面特有业务逻辑 - 处理模式切换时的状态重置
 */
const switchMode = (mode: 'upload' | 'link') => {
  currentMode.value = mode
  // 重置状态
  selectedVideo.value = null
  resetUploadState()
}

/**
 * 获取当前模式的处理描述文本
 * 页面特有业务逻辑 - 根据上传模式显示不同的提示文本
 */
const getProcessingDesc = () => {
  if (currentMode.value === 'link') {
    return '后端正在下载视频并处理...'
  }
  return '正在上传视频文件并处理...'
}

/**
 * 获取上传阶段的描述文本
 * 根据上传进度显示不同的提示信息
 */
const getUploadDesc = () => {
  if (uploadProgress.value === 0) {
    return '正在连接服务器，准备上传...'
  } else if (uploadProgress.value < 50) {
    return '文件上传中，请保持网络连接稳定'
  } else if (uploadProgress.value < 100) {
    return '即将上传完成，正在进行最后处理...'
  } else {
    return '上传完成，正在启动AI处理任务...'
  }
}

/**
 * 处理视频链接解析结果
 * 页面特有业务逻辑 - 处理从VideoLinkParser组件传来的解析结果
 * 包括创建任务、下载视频、更新进度等完整流程
 */
const handleParsedVideo = async (result: any) => {
  console.log('接收到解析结果:', result)
  console.log('解析结果结构:', JSON.stringify(result, null, 2))

  let progressInterval: number | null = null;

  try {
    // 检查解析结果是否为视频
    if (!result.videoUrl) {
      console.log('视频对象检查失败:', { 
        hasVideo: !!result.videoUrl, 
        hasVideoUrl: !!(result.videoUrl),
        resultType: result.type,
        fullResult: result
      })
      
      throw new Error(result.type === 'image_gallery' 
        ? '该内容是图集，不是视频。本服务仅支持视频处理。'
        : '解析结果中没有有效的视频地址'
      )
    }
    
    isUploading.value = true
    uploadStatus.value = '正在处理视频...'
    
    // 先创建任务获取taskId
    const taskResult = await uniCloud.callFunction({
      name: 'get-oss-upload-policy',
      data: {
        fileName: result.title ? `${result.title}.mp4` : `parsed_video_${Date.now()}.mp4`,
        fileSize: 0, // 后端下载时会获取实际大小
        duration: 0, // 后端处理时会获取实际时长
        openid: userInfo.value.openid,
        sourceLanguage: selectedSourceLanguage.value,
        targetLanguage: selectedTargetLanguage.value,
        isParsedVideo: true // 标记这是解析的视频
      }
    })

    if (taskResult.result.code !== 0) {
      throw new Error(taskResult.result.message || '创建任务失败')
    }

    const taskId = taskResult.result.data.taskId
    currentTaskId.value = taskId
    
    uploadStatus.value = '正在下载视频...'
    
    // 对于链接解析的视频，设置初始状态显示处理中
    uploadDetails.value.startTime = Date.now();
    uploadProgress.value = 10; // 显示初始进度
    
    // 模拟处理进度（在实际下载完成前给用户反馈）
    progressInterval = setInterval(() => {
      if (uploadProgress.value < 80) {
        uploadProgress.value += 5;
        uploadStatus.value = `正在处理视频... ${uploadProgress.value}%`;
      }
    }, 1000);
    
    // 调用新的云函数下载视频
    const downloadResult = await uniCloud.callFunction({
      name: 'download-parsed-video',
      data: {
        parseResult: result,  // 传递完整的解析结果
        taskId: taskId,
        openid: userInfo.value.openid,
        sourceLanguage: selectedSourceLanguage.value,
        targetLanguage: selectedTargetLanguage.value
      }
    })
    
    console.log('视频下载处理结果:', downloadResult)
    
    // 清除模拟进度定时器
    if (progressInterval !== null) {
      clearInterval(progressInterval);
    }
    
    if (downloadResult.result.code === 0) {
      // 更新文件大小信息（从云函数返回的数据中获取）
      if (downloadResult.result.data.fileInfo) {
        const fileInfo = downloadResult.result.data.fileInfo;
        uploadDetails.value.totalSize = fileInfo.fileSize || 0;
        uploadDetails.value.uploadedSize = fileInfo.fileSize || 0; // 完成时设置为总大小
      }
      
      // 显示成功状态
      uploadProgress.value = 100
      uploadSuccess.value = true
      uploadStatus.value = '视频处理成功！'
      
      // 跳转到处理页面
      setTimeout(() => {
        uni.navigateTo({
          url: `/pages/process/process?taskId=${taskId}&ossUrl=${encodeURIComponent(downloadResult.result.data.ossUrl)}`,
          success: () => {
            resetUploadState()
          },
          fail: (error) => {
            console.error('页面跳转失败:', error)
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            })
            resetUploadState()
          }
        })
      }, 1000)
    } else {
      throw new Error(downloadResult.result.message || '视频下载失败')
    }
    
  } catch (error: any) {
    console.error('处理解析视频失败:', error)
    
    // 清除模拟进度定时器
    if (progressInterval !== null) {
      clearInterval(progressInterval);
    }
    
    let errorTitle = '视频处理失败'
    let errorMessage = error.message || '视频下载处理失败'
    let suggestions = [
      '检查网络连接是否稳定',
      '确认视频链接仍然有效',
      '稍后重试或尝试其他视频'
    ]
    
    // 根据错误类型提供更具体的建议
    if (error.message.includes('图集')) {
      errorTitle = '不支持的内容类型'
      suggestions = [
        '请选择包含视频的链接',
        '确认分享的是视频而不是图片',
        '尝试其他视频链接'
      ]
    } else if (error.message.includes('创建任务失败')) {
      errorTitle = '任务创建失败'
      suggestions = [
        '检查登录状态是否正常',
        '稍后重试',
        '联系客服获取帮助'
      ]
    }
    
    showError({
      type: 'error',
      title: errorTitle,
      message: errorMessage,
      details: '后端处理视频时遇到问题，请稍后重试',
      suggestions: suggestions,
      showRetry: true,
      retryAction: () => handleParsedVideo(result)
    })
    
    resetUploadState()
  }
}


/**
 * 处理视频链接解析错误
 * 页面特有业务逻辑 - 根据不同错误类型提供相应的用户建议
 */
const handleParseError = (error: string) => {
  console.error('视频解析错误:', error)
  
  // 根据错误类型提供不同的建议
  let suggestions: string[] = []
  let details = ''
  
  if (error.includes('不支持的链接格式')) {
    suggestions = [
      '确认链接来自抖音或小红书官方app',
      '检查链接是否完整且未过期',
      '尝试复制最新的分享链接'
    ]
    details = '当前仅支持抖音(douyin.com)和小红书(xiaohongshu.com)的视频分享链接'
  } else if (error.includes('网络')) {
    suggestions = [
      '检查网络连接是否正常',
      '尝试切换到更稳定的网络环境',
      '稍后重试'
    ]
    details = '网络连接异常，无法访问视频平台服务器'
  } else if (error.includes('解析失败')) {
    suggestions = [
      '确认视频链接有效且可正常访问',
      '检查视频是否已被删除或设为私密',
      '尝试使用其他视频链接'
    ]
    details = '视频解析过程中遇到问题，可能是视频不可访问或链接格式异常'
  } else {
    suggestions = [
      '检查网络连接',
      '确认链接格式正确',
      '稍后重试或尝试其他视频'
    ]
    details = error
  }
  
  showError({
    type: 'error',
    title: '视频解析失败',
    message: error,
    details: details,
    suggestions: suggestions,
    showRetry: true,
    retryAction: () => {
      // 重试时清空当前状态，让用户重新尝试
      resetUploadState()
    }
  })
}

// 页面加载时获取用户信息
onMounted(async () => {
  await loadUserInfo()
})

// 页面显示时检查登录状态
onShow(async () => {
  console.log('上传页面显示，检查登录状态')
  await checkAndUpdateLoginStatus()
})

// 加载用户信息（保持原有逻辑，用于页面初始化）
const loadUserInfo = async () => {
  try {
    // 从本地存储获取用户信息
    const savedUserInfo = uni.getStorageSync('userInfo')
    if (savedUserInfo && savedUserInfo.openid) {
      userInfo.value.openid = savedUserInfo.openid
      userInfo.value.isLogin = true
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

// 检查并更新登录状态（用于onShow生命周期）
const checkAndUpdateLoginStatus = async () => {
  try {
    const loginCheck: LoginCheckResult = await checkLoginStatus()

    // 更新本地登录状态
    userInfo.value.isLogin = loginCheck.isLogin
    userInfo.value.openid = loginCheck.openid || ''

    // 如果登录状态发生变化，处理相应逻辑
    if (!loginCheck.isLogin) {
      console.log('用户未登录或登录已过期:', loginCheck.reason)

      // 如果用户之前是登录状态但现在检查失败，显示提示
      if (loginCheck.needRelogin) {
        handleLoginCheck(loginCheck, {
          showToast: true,
          autoRedirect: false,
          customMessage: '登录已过期，请重新登录后使用上传功能'
        })
      }

      // 重置上传状态，防止在未登录状态下继续操作
      if (isUploading.value) {
        resetUploadState()
      }
    } else {
      console.log('用户登录状态正常，openid:', loginCheck.openid)
    }
  } catch (error) {
    console.error('检查登录状态失败:', error)
    // 发生错误时，保守处理，设置为未登录状态
    userInfo.value.isLogin = false
    userInfo.value.openid = ''
  }
}

// 跳转到登录页面
const goToLogin = () => {
  uni.switchTab({
    url: '/pages/profile/profile'
  })
}





/**
 * 重置上传状态
 * 页面特有业务逻辑 - 清理所有上传相关的状态数据
 */
const resetUploadState = () => {
  isUploading.value = false
  uploadSuccess.value = false
  uploadProgress.value = 0
  uploadStatus.value = ''
  uploadDetails.value = {
    uploadedSize: 0,
    totalSize: 0,
    uploadSpeed: 0,
    remainingTime: 0,
    startTime: 0
  }
}



// 选择视频文件
const selectVideo = async () => {
  // 检查用户是否已登录
  if (!userInfo.value.isLogin || !userInfo.value.openid) {
    showError({
      type: 'warning',
      title: '需要登录',
      message: '请先登录后再使用上传功能',
      suggestions: [
        '点击右下角"个人中心"进行微信登录',
        '登录后即可享受完整服务'
      ],
      showRetry: false
    })
    return
  }

  try {
    // 使用uni.chooseVideo选择视频，使用配置的时长限制
    const chooseResult = await uni.chooseVideo({
      maxDuration: 60,
      sourceType: ['album', 'camera'],
      compressed: false,
      camera: 'back' 
    })

    console.log('选择视频结果：', chooseResult)

    const { tempFilePath, duration, size, name, width, height } = chooseResult

    // 检查视频时长
    if (duration > uploadConfig.maxDuration) {
      showError({
        type: 'warning',
        title: '视频时长超限',
        message: `视频时长不能超过${formatMaxDuration(uploadConfig)}`,
        details: `当前视频时长：${Math.floor(duration)}秒，最大允许：${uploadConfig.maxDuration}秒`,
        suggestions: [
          '使用视频编辑工具裁剪视频',
          '选择时长更短的视频片段',
          '分段处理长视频'
        ],
        showRetry: true,
        retryAction: selectVideo
      })
      return
    }

    // 检查文件大小
    if (size > uploadConfig.maxFileSize) {
      showError({
        type: 'warning',
        title: '文件大小超限',
        message: `文件大小不能超过${formatMaxFileSize(uploadConfig)}`,
        details: `当前文件大小：${Math.floor(size / 1024 / 1024)}MB，最大允许：${Math.floor(uploadConfig.maxFileSize / 1024 / 1024)}MB`,
        suggestions: [
          '使用视频压缩工具减小文件大小',
          '选择分辨率较低的视频',
          '尝试其他格式的视频文件'
        ],
        showRetry: true,
        retryAction: selectVideo
      })
      return
    }

    selectedVideo.value = {
      tempFilePath,
      duration,
      size,
      name: name || `video_${Date.now()}.mp4`,
      width,
      height
    }

    // 获取上传凭证
    await getUploadAuth()

  } catch (error: any) {
    console.error('选择视频失败：', error)
    
    let errorMessage = '选择视频失败'
    let suggestions = ['重新尝试选择视频', '检查相册权限设置']
    
    if (error.errMsg) {
      if (error.errMsg.includes('cancel')) {
        return // 用户取消，不显示错误
      } else if (error.errMsg.includes('permission')) {
        errorMessage = '没有访问相册的权限'
        suggestions = [
          '前往手机设置开启相册访问权限',
          '重新启动应用后再试'
        ]
      } else if (error.errMsg.includes('not support')) {
        errorMessage = '当前环境不支持选择视频'
        suggestions = [
          '更新应用到最新版本',
          '尝试使用其他设备'
        ]
      }
    }
    
    showError({
      type: 'error',
      title: '选择视频失败',
      message: errorMessage,
      details: error.errMsg || error.message || '未知错误',
      suggestions: suggestions,
      showRetry: true,
      retryAction: selectVideo
    })
  }
}

// 获取上传凭证
const getUploadAuth = async () => {
  if (!selectedVideo.value) {
    return
  }

  // 检查用户是否已登录
  if (!userInfo.value.isLogin || !userInfo.value.openid) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    // 跳转到登录页面
    uni.switchTab({
      url: '/pages/profile/profile'
    })
    return
  }

  try {
    isUploading.value = true
    uploadStatus.value = '正在获取上传策略...'

    // 调用云函数获取OSS上传策略
    console.log('上传参数:', {
      fileName: selectedVideo.value.name,
      fileSize: selectedVideo.value.size,
      duration: selectedVideo.value.duration,
      videoWidth: selectedVideo.value.width,
      videoHeight: selectedVideo.value.height,
      sourceLanguage: selectedSourceLanguage.value,
      targetLanguage: selectedTargetLanguage.value
    })

    const result = await uniCloud.callFunction({
      name: 'get-oss-upload-policy',
      data: {
        fileName: selectedVideo.value.name,
        fileSize: selectedVideo.value.size,
        duration: selectedVideo.value.duration,
        openid: userInfo.value.openid,
        sourceLanguage: selectedSourceLanguage.value,
        targetLanguage: selectedTargetLanguage.value,
        videoWidth: selectedVideo.value.width,
        videoHeight: selectedVideo.value.height
      }
    })

    console.log('获取OSS上传策略结果：', result)

    if (result.result.code === 0) {
      const { taskId, accessKeyId, host, policy, signature, key, fullUrl, fileInfo } = result.result.data

      // 开始OSS直传
      await startOSSUpload({
        taskId,
        host,
        accessKeyId,
        policy,
        signature,
        key,
        fullUrl,
        fileInfo
      })

    } else {
      uni.hideLoading()
      uni.showToast({
        title: result.result.message || '获取上传策略失败',
        icon: 'none'
      })
    }

  } catch (error) {
    console.error('获取上传凭证失败：', error)
    uni.hideLoading()
    uni.showToast({
      title: '获取凭证失败',
      icon: 'none'
    })
  } finally {
    isUploading.value = false
  }
}

// 开始OSS直传
const startOSSUpload = async (uploadInfo: {
  taskId: string
  host: string
  accessKeyId: string
  policy: string
  signature: string
  key: string
  fullUrl: string
  fileInfo: {
    fileName: string
    fileSize: number
    duration: number
    userId: string
  }
}) => {
  try {
    uploadStatus.value = '正在上传视频...'
    uploadProgress.value = 0

    // 初始化上传详细信息
    uploadDetails.value = {
      uploadedSize: 0,
      totalSize: selectedVideo.value?.size || 0,
      uploadSpeed: 0,
      remainingTime: 0,
      startTime: Date.now()
    }

    console.log('开始OSS直传:', uploadInfo)

    // 使用 uni.uploadFile 直传到OSS
    const uploadTask = uni.uploadFile({
      url: uploadInfo.host, // OSS的域名
      filePath: selectedVideo.value!.tempFilePath,
      name: 'file', // 这个name必须是'file'
      formData: {
        'key': uploadInfo.key,
        'policy': uploadInfo.policy,
        'OSSAccessKeyId': uploadInfo.accessKeyId,
        'signature': uploadInfo.signature,
        'success_action_status': '200' // 让OSS返回200状态码
      },
      success: async (uploadRes) => {
        console.log('OSS上传响应:', uploadRes)

        if (uploadRes.statusCode === 204 || uploadRes.statusCode === 200) {
          console.log('上传成功!')
          uploadProgress.value = 100

          // 更新最终的上传详情
          uploadDetails.value.uploadedSize = uploadDetails.value.totalSize
          uploadDetails.value.remainingTime = 0
          uploadDetails.value.uploadSpeed = uploadDetails.value.totalSize / ((Date.now() - uploadDetails.value.startTime) / 1000)

          // 显示上传成功状态
          uploadSuccess.value = true
          uploadStatus.value = '上传成功！正在启动AI处理任务...'

          try {
            const taskId = uploadInfo.taskId
            console.log('使用已创建的taskId:', taskId)
            currentTaskId.value = taskId

            // 更新任务状态为extracting_audio，并保存OSS URL和完整文件信息
            const updateResult = await uniCloud.callFunction({
              name: 'update-task-status',
              data: {
                taskId: taskId,
                status: 'extracting_audio',
                ossUrl: uploadInfo.fullUrl,
                openid: userInfo.value.openid,
                // 添加完整的文件信息，确保任务记录包含所有必要的元数据
                fileInfo: {
                  fileName: selectedVideo.value?.name || '',
                  fileSize: selectedVideo.value?.size || 0,
                  duration: selectedVideo.value?.duration || 0,
                  videoWidth: selectedVideo.value?.width || 0,
                  videoHeight: selectedVideo.value?.height || 0
                }
              }
            })

            console.log('更新任务状态结果:', updateResult)

            if (updateResult.result.code === 200) {
              // 启动音频提取任务（异步，不等待结果）
              uniCloud.callFunction({
                name: 'process-video-task',
                data: {
                  taskId: taskId,
                  ossUrl: uploadInfo.fullUrl,
                  action: 'extract_audio'
                }
              }).then(processResult => {
                console.log('调用音频提取任务结果:', processResult)
              }).catch(error => {
                console.error('启动处理任务失败:', error)
              })

              // 立即跳转到处理页面，不等待处理任务启动
              setTimeout(() => {
                uni.navigateTo({
                  url: `/pages/process/process?taskId=${taskId}&ossUrl=${encodeURIComponent(uploadInfo.fullUrl)}`,
                  success: () => {
                    // 跳转成功后重置状态
                    resetUploadState()
                  },
                  fail: (error) => {
                    console.error('页面跳转失败:', error)
                    uni.showToast({
                      title: '页面跳转失败',
                      icon: 'none'
                    })
                    resetUploadState()
                  }
                })
              }, 1000) // 减少等待时间到1秒，让用户看到成功动画
            } else {
              throw new Error(updateResult.result.message || '更新任务状态失败')
            }
          } catch (error) {
            console.error('处理失败:', error)
            uploadSuccess.value = false
            uploadStatus.value = '处理失败'
            uni.showToast({
              title: '处理失败: ' + (error instanceof Error ? error.message : '未知错误'),
              icon: 'none'
            })
            // 3秒后重置状态，允许用户重新上传
            setTimeout(() => {
              resetUploadState()
            }, 3000)
          }
        } else {
          console.error('上传失败，状态码:', uploadRes.statusCode, uploadRes.data)
          uploadStatus.value = '上传失败'
          uni.showToast({
            title: `上传失败: ${uploadRes.statusCode}`,
            icon: 'none'
          })
          // 3秒后重置状态
          setTimeout(() => {
            resetUploadState()
          }, 3000)
        }
      },
      fail: (err) => {
        console.error('上传API调用失败:', err)
        uploadStatus.value = '上传失败'
        uni.showToast({
          title: '上传请求失败',
          icon: 'none'
        })
        // 3秒后重置状态
        setTimeout(() => {
          resetUploadState()
        }, 3000)
      },
      complete: () => {
        // 只有在非成功状态下才重置 isUploading
        if (!uploadSuccess.value) {
          isUploading.value = false
        }
      }
    })

    // 监听上传进度
    uploadTask.onProgressUpdate((progressRes) => {
      const currentTime = Date.now()
      const progress = Math.round(progressRes.progress) // 确保进度是整数
      const uploadedBytes = progressRes.totalBytesSent // 已上传的字节数
      const totalBytes = progressRes.totalBytesExpectedToSend // 总字节数

      // 更新基本进度
      uploadProgress.value = progress
      uploadStatus.value = `上传中... ${progress}%`

      // 更新上传详细信息
      uploadDetails.value.uploadedSize = uploadedBytes
      uploadDetails.value.totalSize = totalBytes

      // 计算上传速度（字节/秒）
      const elapsedTime = (currentTime - uploadDetails.value.startTime) / 1000
      if (elapsedTime > 1) { // 至少等待1秒再计算速度，避免初始波动
        uploadDetails.value.uploadSpeed = uploadedBytes / elapsedTime

        // 计算剩余时间
        const remainingBytes = totalBytes - uploadedBytes
        if (uploadDetails.value.uploadSpeed > 0 && remainingBytes > 0) {
          uploadDetails.value.remainingTime = remainingBytes / uploadDetails.value.uploadSpeed
        } else {
          uploadDetails.value.remainingTime = 0
        }
      }

      console.log('上传进度详情:', {
        progress: progress + '%',
        uploaded: formatFileSize(uploadedBytes),
        total: formatFileSize(totalBytes),
        speed: formatSpeed(uploadDetails.value.uploadSpeed),
        remaining: formatRemainingTime(uploadDetails.value.remainingTime),
        elapsedTime: elapsedTime.toFixed(1) + 's'
      })
    })

  } catch (error) {
    console.error('上传过程出错:', error)
    uploadStatus.value = '上传失败'

    // 显示错误提示和重试选项
    uni.showModal({
      title: '上传失败',
      content: '网络连接异常或文件上传失败，是否重新尝试？',
      confirmText: '重试',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 重试上传
          console.log('用户选择重试上传')
          uploadToOSS(selectedVideo.value)
        } else {
          // 取消，重置状态
          resetUploadState()
        }
      },
      fail: () => {
        // 模态框显示失败，直接重置状态
        resetUploadState()
      }
    })
  }
}

</script>

<style scoped>
/* ==================== 全局容器和基础样式 ==================== */
.upload-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #fafbff 0%, #f0f4ff 50%, #e0e7ff 100%);
  position: relative;
  overflow: hidden;
}

.upload-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.06) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* ==================== 现代化头部区域 ==================== */
.hero-header {
  position: relative;
  padding: 40rpx 32rpx 32rpx;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  border-radius: 0 0 32rpx 32rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  z-index: 2;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
}

.hero-icon {
  position: relative;
  display: inline-block;
  margin-bottom: 24rpx;
}

.icon-video {
  font-size: 64rpx;
  display: block;
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 8rpx 16rpx rgba(0, 0, 0, 0.2));
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100rpx;
  height: 100rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: glow-pulse 3s ease-in-out infinite;
}

@keyframes glow-pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.6; }
  50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.8; }
}

.hero-text {
  margin-bottom: 24rpx;
}

.hero-title {
  display: block;
  font-size: 44rpx;
  font-weight: 800;
  color: white;
  margin-bottom: 12rpx;
  line-height: 1.2;
  letter-spacing: -0.5rpx;
}

.hero-subtitle {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  line-height: 1.4;
}

.hero-stats {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24rpx;
  margin-top: 24rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 28rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 6rpx;
}

.stat-label {
  display: block;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.stat-divider {
  width: 2rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 1rpx;
}

.hero-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border: 2rpx solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: -100rpx;
  right: -100rpx;
  animation: float 6s ease-in-out infinite;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  bottom: -75rpx;
  left: -75rpx;
  animation: float 8s ease-in-out infinite reverse;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 50%;
  right: 20rpx;
  animation: float 4s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20rpx) rotate(180deg); }
}

/* ==================== 内容包装器 ==================== */
.content-wrapper {
  position: relative;
  z-index: 2;
  padding: 0 32rpx 32rpx;
  min-height: 60vh;
}

/* ==================== 未登录状态 ==================== */
.auth-section {
  margin-bottom: 40rpx;
}

.auth-card {
  background: white;
  border-radius: 32rpx;
  padding: 48rpx 32rpx;
  box-shadow: 
    0 4rpx 24rpx rgba(0, 0, 0, 0.06),
    0 0 0 1rpx rgba(99, 102, 241, 0.1);
  position: relative;
  overflow: hidden;
}

.auth-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
}

.auth-visual {
  text-align: center;
  margin-bottom: 32rpx;
}

.auth-icon {
  position: relative;
  display: inline-block;
}

.lock-icon {
  font-size: 64rpx;
  display: block;
  position: relative;
  z-index: 2;
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100rpx;
  height: 100rpx;
  border: 2rpx solid #6366f1;
  border-radius: 50%;
  animation: pulse-ring 2s ease-out infinite;
}

@keyframes pulse-ring {
  0% { transform: translate(-50%, -50%) scale(0.5); opacity: 1; }
  100% { transform: translate(-50%, -50%) scale(1.5); opacity: 0; }
}

.auth-content {
  text-align: center;
  margin-bottom: 40rpx;
}

.auth-title {
  display: block;
  font-size: 44rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16rpx;
  line-height: 1.2;
}

.auth-description {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 32rpx;
}

.auth-features {
  display: flex;
  justify-content: center;
  gap: 32rpx;
  flex-wrap: wrap;
}

.feature-point {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.feature-icon {
  font-size: 24rpx;
}

.feature-text {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
}

.auth-button {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  border: none;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(99, 102, 241, 0.3);
  transition: all 0.3s ease;
}

.auth-button:active {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(99, 102, 241, 0.4);
}

.btn-text {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
}

.btn-arrow {
  font-size: 24rpx;
  color: white;
  transition: transform 0.3s ease;
}

.auth-button:active .btn-arrow {
  transform: translateX(4rpx);
}

/* ==================== 模式选择器 ==================== */
.mode-selector {
  margin-bottom: 40rpx;
}

.selector-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.selector-title {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.selector-subtitle {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
}

.mode-cards {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.mode-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  border: 2rpx solid #f3f4f6;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.mode-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(99, 102, 241, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.mode-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.08);
}

.mode-card.card-active {
  border-color: #6366f1;
  box-shadow: 0 8rpx 32rpx rgba(99, 102, 241, 0.15);
}

.mode-card.card-active::before {
  opacity: 1;
}

.card-visual {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
}

.visual-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.upload-visual {
  background: linear-gradient(135deg, #ddd6fe 0%, #c4b5fd 100%);
}

.link-visual {
  background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
}

.card-emoji {
  font-size: 32rpx;
}

.visual-decoration {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.deco-dot {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: rgba(99, 102, 241, 0.3);
  border-radius: 50%;
}

.dot-1 { top: -40rpx; left: -20rpx; animation: float-dot 3s ease-in-out infinite; }
.dot-2 { top: -20rpx; right: -30rpx; animation: float-dot 3s ease-in-out infinite 1s; }
.dot-3 { bottom: -30rpx; left: -10rpx; animation: float-dot 3s ease-in-out infinite 2s; }

.deco-line {
  position: absolute;
  width: 20rpx;
  height: 2rpx;
  background: rgba(59, 130, 246, 0.3);
  border-radius: 1rpx;
}

.line-1 { top: -25rpx; left: -15rpx; transform: rotate(45deg); }
.line-2 { bottom: -25rpx; right: -15rpx; transform: rotate(-45deg); }

@keyframes float-dot {
  0%, 100% { transform: translateY(0); opacity: 0.5; }
  50% { transform: translateY(-8rpx); opacity: 1; }
}

.card-content {
  padding-right: 80rpx;
}

.card-title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.card-description {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.card-badges {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.badge {
  padding: 6rpx 16rpx;
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  font-size: 22rpx;
  font-weight: 600;
  border-radius: 12rpx;
  border: 1rpx solid rgba(99, 102, 241, 0.2);
}

.card-indicator {
  position: absolute;
  bottom: 24rpx;
  right: 24rpx;
}

.indicator-ring {
  width: 24rpx;
  height: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 50%;
  position: relative;
}

.indicator-dot {
  width: 12rpx;
  height: 12rpx;
  background: #6366f1;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.3s ease;
}

.card-active .indicator-dot {
  transform: translate(-50%, -50%) scale(1);
}

/* ==================== 上传区域 ==================== */
.upload-section {
  margin-bottom: 40rpx;
}

/* 上传禁用状态样式 */
.upload-disabled-section {
  margin-bottom: 40rpx;
}

.upload-disabled-zone {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2rpx dashed #dee2e6;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.disabled-visual {
  margin-bottom: 40rpx;
}

.disabled-circle {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  border-radius: 50%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 8rpx 32rpx rgba(108, 117, 125, 0.3);
}

.disabled-icon {
  font-size: 48rpx;
  line-height: 1;
}

.disabled-content {
  text-align: center;
}

.disabled-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #495057;
  margin-bottom: 16rpx;
  display: block;
}

.disabled-subtitle {
  font-size: 28rpx;
  color: #6c757d;
  line-height: 1.5;
  display: block;
}

.upload-zone {
  background: white;
  border: 3rpx dashed #d1d5db;
  border-radius: 32rpx;
  padding: 80rpx 32rpx;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.upload-zone::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(139, 92, 246, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.upload-zone:active {
  border-color: #6366f1;
  background: rgba(99, 102, 241, 0.02);
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 48rpx rgba(99, 102, 241, 0.1);
}

.upload-zone:active::before {
  opacity: 1;
}

.upload-visual {
  margin-bottom: 32rpx;
  position: relative;
  z-index: 2;
}

.upload-circle {
  position: relative;
  display: inline-block;
}

.upload-icon {
  font-size: 80rpx;
  display: block;
  position: relative;
  z-index: 3;
}

.upload-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2rpx solid rgba(99, 102, 241, 0.2);
  border-radius: 50%;
  animation: expand-ring 3s ease-out infinite;
}

.ring-1 { width: 120rpx; height: 120rpx; }
.ring-2 { width: 160rpx; height: 160rpx; animation-delay: 1s; }
.ring-3 { width: 200rpx; height: 200rpx; animation-delay: 2s; }

@keyframes expand-ring {
  0% { transform: translate(-50%, -50%) scale(0.3); opacity: 1; }
  100% { transform: translate(-50%, -50%) scale(1); opacity: 0; }
}

.upload-content {
  position: relative;
  z-index: 2;
}

.upload-title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.upload-subtitle {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 32rpx;
}

.upload-specs {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16rpx;
  flex-wrap: wrap;
}

.spec-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.spec-icon {
  font-size: 24rpx;
}

.spec-text {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
}

.spec-divider {
  color: #d1d5db;
  font-weight: 500;
}

/* ==================== 链接解析区域 ==================== */
.link-section {
  margin-bottom: 40rpx;
}

/* ==================== 进度区域 ==================== */
.progress-section {
  margin-bottom: 40rpx;
}

.progress-card {
  background: white;
  border-radius: 32rpx;
  padding: 48rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(99, 102, 241, 0.1);
  position: relative;
  overflow: hidden;
}

.progress-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
}

.progress-visual {
  text-align: center;
  margin-bottom: 40rpx;
}

.progress-circle {
  position: relative;
  display: inline-block;
  width: 160rpx;
  height: 160rpx;
}

.circle-progress {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(
    #6366f1 0deg,
    #6366f1 calc(var(--progress) * 3.6deg),
    #f3f4f6 calc(var(--progress) * 3.6deg),
    #f3f4f6 360deg
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.circle-progress::before {
  content: '';
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  right: 10rpx;
  bottom: 10rpx;
  background: white;
  border-radius: 50%;
}

.progress-percent {
  font-size: 32rpx;
  font-weight: 700;
  color: #1f2937;
  position: relative;
  z-index: 2;
}

.pulse-waves {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.wave {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2rpx solid rgba(99, 102, 241, 0.3);
  border-radius: 50%;
  animation: pulse-wave 2s ease-out infinite;
}

.wave-1 { width: 200rpx; height: 200rpx; }
.wave-2 { width: 240rpx; height: 240rpx; animation-delay: 0.5s; }
.wave-3 { width: 280rpx; height: 280rpx; animation-delay: 1s; }

@keyframes pulse-wave {
  0% { transform: translate(-50%, -50%) scale(0.8); opacity: 1; }
  100% { transform: translate(-50%, -50%) scale(1.2); opacity: 0; }
}

.progress-info {
  text-align: center;
}

.progress-title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.progress-subtitle {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 32rpx;
}

.progress-details {
  background: #f9fafb;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 26rpx;
  color: #6b7280;
  font-weight: 500;
}

.detail-value {
  font-size: 26rpx;
  color: #1f2937;
  font-weight: 600;
}

.preparing-stage {
  text-align: center;
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.dot {
  width: 8rpx;
  height: 8rpx;
  background: #6366f1;
  border-radius: 50%;
  animation: loading-dot 1.5s ease-in-out infinite;
}

.dot-2 { animation-delay: 0.3s; }
.dot-3 { animation-delay: 0.6s; }

@keyframes loading-dot {
  0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
  40% { transform: scale(1.2); opacity: 1; }
}

.preparing-text {
  font-size: 26rpx;
  color: #6b7280;
}

/* ==================== 成功状态 ==================== */
.success-section {
  margin-bottom: 40rpx;
}

.success-card {
  background: white;
  border-radius: 32rpx;
  padding: 64rpx 32rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(34, 197, 94, 0.2);
  position: relative;
  overflow: hidden;
}

.success-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #22c55e, #16a34a);
}

.success-visual {
  margin-bottom: 32rpx;
}

.success-circle {
  position: relative;
  display: inline-block;
}

.success-emoji {
  font-size: 80rpx;
  display: block;
  position: relative;
  z-index: 2;
  animation: success-bounce 0.6s ease-out;
}

@keyframes success-bounce {
  0% { transform: scale(0.3); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.success-sparkles {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.sparkle {
  position: absolute;
  font-size: 24rpx;
  animation: sparkle-float 2s ease-in-out infinite;
}

.sparkle-1 { top: -60rpx; left: -40rpx; animation-delay: 0s; }
.sparkle-2 { top: -40rpx; right: -50rpx; animation-delay: 0.5s; }
.sparkle-3 { bottom: -50rpx; left: -30rpx; animation-delay: 1s; }
.sparkle-4 { bottom: -40rpx; right: -40rpx; animation-delay: 1.5s; }

@keyframes sparkle-float {
  0%, 100% { transform: translateY(0) scale(1); opacity: 0.6; }
  50% { transform: translateY(-16rpx) scale(1.2); opacity: 1; }
}

.success-content {
  position: relative;
  z-index: 2;
}

.success-title {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #22c55e;
  margin-bottom: 16rpx;
}

.success-description {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* ==================== 设置面板 ==================== */
.settings-panel {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* ==================== 语言配置卡片 ==================== */
.language-config-card {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
  border: 2rpx solid #e2e8f0;
  margin-bottom: 32rpx;
  position: relative;
}

.language-config-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
}

.config-header {
  display: flex;
  align-items: center;
  padding: 28rpx 32rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1rpx solid #e2e8f0;
}

.config-icon {
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(99, 102, 241, 0.3);
}

.config-content {
  flex: 1;
}

.config-title {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.config-subtitle {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
}

.config-body {
  padding: 32rpx;
}

/* ==================== 处理能力面板 ==================== */
.capabilities-panel {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.panel-section {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
}

.panel-header {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f3f4f6;
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
}

.header-icon {
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.icon-text {
  font-size: 28rpx;
}

.header-content {
  flex: 1;
}

.panel-title {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.panel-subtitle {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
}

.panel-body {
  padding: 32rpx;
}

.capability-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.capability-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 32rpx 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);
  border-radius: 20rpx;
  border: 2rpx solid #f1f5f9;
  transition: all 0.3s ease;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.capability-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.capability-item:hover::before,
.capability-item:active::before {
  opacity: 1;
}

.capability-item:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 32rpx rgba(99, 102, 241, 0.15);
  border-color: #e2e8f0;
}

.capability-icon {
  font-size: 40rpx;
  width: 80rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(99, 102, 241, 0.3);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.capability-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
  border-radius: 20rpx;
}

.capability-info {
  flex: 1;
  min-width: 0;
  text-align: center;
}

.capability-title {
  display: block;
  font-size: 28rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8rpx;
  line-height: 1.3;
}

.capability-desc {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
  font-weight: 500;
}

</style>
